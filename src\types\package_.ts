export interface IActivity {
  id: string;
  name: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
}

export interface IRegion {
  id: string;
  name: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageHighlights {
  id: string;
  packageId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageDescription {
  id: string;
  packageId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageShortItinerary {
  id: string;
  packageId: string;
  title: string;
  points: string[];
  createdAt: string;
  updatedAt: string;
}

export interface IPackageGalleryImage {
  id: string;
  packageGalleryId: string;
  src: string;
  caption: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageGallery {
  id: string;
  packageId: string;
  title: string;
  PackageGalleryImage: IPackageGalleryImage[];
  createdAt: string;
  updatedAt: string;
}

export interface IPackageYtVideo {
  id: string;
  packageId: string;
  title: string;
  links: string[];
  createdAt: string;
  updatedAt: string;
}

export interface IPackageInclusions {
  id: string;
  packageId: string;
  title: string;
  details: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageExclusions {
  id: string;
  packageId: string;
  title: string;
  details: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageMap {
  id: string;
  packageId: string;
  title: string;
  map: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageInfoItem {
  id: string;
  packageInfoId: string;
  title: string;
  details: string;
  note: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageInfo {
  id: string;
  packageId: string;
  title: string;
  items: IPackageInfoItem[];
  createdAt: string;
  updatedAt: string;
}

export interface IPackageItinerary {
  id: string;
  packageId: string;
  heading: string;
  day: number;
  title: string;
  activity: string;
  trekDistance: string;
  flightHours: string;
  drivingHours: string;
  highestAltitude: string;
  trekDuration: string;
  image: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageEquipment {
  id: string;
  packageId: string;
  title: string;
  description: string;
  head: string;
  body: string;
  face: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface IPackageCostDate {
  id: string;
  packageId: string;
  days: string;
  startDate: string;
  endDate: string;
  price: string;
  discountPrice: string;
  tripStatus: string;
  published: boolean;
  markUpcomingTreks: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageGroupPrice {
  id: string;
  packageId: string;
  numberOfPeople: string;
  pricePerPerson: string;
  note: string;
  published: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageFaq {
  id: string;
  packageId: string;
  question: string;
  answer: string;
  published: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IPackageReview {
  id: string;
  packageId: string;
  name: string;
  email: string;
  rating: number;
  comment: string;
  reviewImage: string;
  published: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IPackage {
  id: string;
  name: string;
  slug: string;
  activityId: string;
  regionId: string;
  accomodation: string;
  distance: string;
  type: string;
  duration: string;
  altitude: string;
  meals: string;
  groupSize: string;
  price: string;
  discountPrice: string;
  bestSeason: string;
  transport: string;
  activityPerDay: string;
  grade: string;
  bookingLink: string;
  overviewDescription: string;
  thumbnail: string;
  mainImage: string;
  mainImageAlt: string;
  pdfBrochure: string;
  published: boolean;
  tripOftheMonth: boolean;
  popularTour: boolean;
  shortTrek: boolean;
  createdAt: string;
  updatedAt: string;

  activity: IActivity;
  region: IRegion;
  highlights?: IPackageHighlights;
  description?: IPackageDescription;
  shortItinerary?: IPackageShortItinerary;
  gallery?: IPackageGallery;
  ytVideo?: IPackageYtVideo;
  inclusions?: IPackageInclusions;
  exclusions?: IPackageExclusions;
  map?: IPackageMap;
  info?: IPackageInfo;

  itinerary: IPackageItinerary[];
  equipment: IPackageEquipment[];
  costDate: IPackageCostDate[];
  groupPrice: IPackageGroupPrice[];
  faq: IPackageFaq[];
  review: IPackageReview[];
}
