'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { useQueryClient } from '@tanstack/react-query'
import { AddEquipmentForm } from '@/modules/product/component/add-equipment-form'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { useGetPackageEquipments } from '@/modules/package-equipment/queries/get-package-equipments'
import { useCreatePackageEquipment } from '@/modules/package-equipment/mutations/create-package-equipment'
import { useUpdatePackageEquipment } from '@/modules/package-equipment/mutations/update-package-equipment'
import { useDeletePackageEquipment } from '@/modules/package-equipment/mutations/delete-package-equipment'
import { IPackageEquipment } from '@/types/package_'
import { toast } from 'sonner'
import { EquipmentList } from '@/modules/product/component/euqipment-list'

export default function EditEquipmentPage() {
  const { id: packageId } = useParams() as { id: string }

  const { data, isLoading, isError } = useGetPackageEquipments()

  const createMutation = useCreatePackageEquipment()
  const updateMutation = useUpdatePackageEquipment(packageId)
  const deleteMutation = useDeletePackageEquipment(packageId)

  const [items, setItems] = useState<IPackageEquipment[]>([])
  const [editingItem, setEditingItem] = useState<IPackageEquipment | null>(null)

  useEffect(() => {
    if (data?.data) {
      const equipments: IPackageEquipment[] = Array.isArray(data.data)
        ? data.data.filter(Boolean)
        : [data.data]
      setItems(equipments)
    }
  }, [data])

  const onAddEquipment = (newItem: Omit<IPackageEquipment, 'id' | 'createdAt' | 'updatedAt'>) => {
    createMutation.mutate(
      { ...newItem, packageId },
      {
        onSuccess: (res) => {
          if (res.data) {
            setItems((prev) => [...prev, res.data])
            toast.success('Equipment added successfully')
          }
        },
        onError: () => toast.error('Failed to add equipment'),
      }
    )
  }

  const onUpdateEquipment = (updatedItem: IPackageEquipment) => {
    updateMutation.mutate(
      updatedItem,
      {
        onSuccess: () => {
          setEditingItem(null)
          toast.success('Equipment updated successfully')
        },
        onError: (error) => {
          console.error('Update failed:', error)
          toast.error('Failed to update equipment')
        },
      }
    )
  }

  const onDeleteEquipment = (id: string) => {
    deleteMutation.mutate(id, {
      onSuccess: () => toast.success('Equipment deleted successfully'),
      onError: () => toast.error('Failed to delete equipment'),
    })
  }

  const onEdit = (id: string) => {
    console.log('DEBUG - Looking for equipment with ID:', id)
    const foundItem = items.find(i => String(i.id) === String(id))
    console.log('DEBUG - Found equipment:', foundItem)
    setEditingItem(foundItem || null)
  }

  if (isLoading) return <div>Loading equipment...</div>
  if (isError) return <div>Error loading equipment data.</div>

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddEquipmentForm
          editingEquipment={editingItem}
          onAddEquipment={onAddEquipment}
          onUpdateEquipment={onUpdateEquipment}
          onCancelEdit={() => setEditingItem(null)}
        />
        <EquipmentList
          items={items}
          onEdit={onEdit}
          onDelete={onDeleteEquipment}
        />
      </div>
    </div>
  )
}